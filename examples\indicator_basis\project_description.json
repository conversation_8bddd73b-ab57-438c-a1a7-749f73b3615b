{"project_name": "indicator_basis", "project_path": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis", "build_dir": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis", "config_file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/sdkconfig", "config_defaults": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/sdkconfig.defaults", "bootloader_elf": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader/bootloader.elf", "app_elf": "indicator_basis.elf", "app_bin": "indicator_basis.bin", "build_type": "flash_app", "git_revision": "v5.1", "target": "esp32s3", "rev": "", "min_rev": "0", "max_rev": "99", "phy_data_partition": "", "monitor_baud": "115200", "monitor_toolprefix": "xtensa-esp32s3-elf-", "config_environment": {"COMPONENT_KCONFIGS": "C:/Espressif/frameworks/esp-idf-v5.1/components/app_trace/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/bt/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/driver/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/efuse/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp-tls/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_adc/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_coex/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_common/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_eth/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_event/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_gdbstub/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_http_client/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_http_server/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_https_ota/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_https_server/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_hw_support/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_lcd/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_netif/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_partition/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_phy/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_pm/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_psram/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_ringbuf/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_system/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_timer/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_wifi/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/espcoredump/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/fatfs/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/freertos/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/hal/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/heap/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/ieee802154/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/log/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/lwip/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/mbedtls/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/mqtt/esp-mqtt/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/newlib/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/nvs_flash/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/openthread/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/protocomm/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/pthread/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/soc/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/spi_flash/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/spiffs/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/tcp_transport/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/ulp/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/unity/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/usb/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/vfs/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/wear_levelling/Kconfig;C:/Espressif/frameworks/esp-idf-v5.1/components/wifi_provisioning/Kconfig;D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/bus/Kconfig;D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/iot_button/Kconfig;D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/lvgl/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "C:/Espressif/frameworks/esp-idf-v5.1/components/bootloader/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_app_format/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.1/components/esp_rom/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.1/components/esptool_py/Kconfig.projbuild;C:/Espressif/frameworks/esp-idf-v5.1/components/partition_table/Kconfig.projbuild;D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/bsp/Kconfig.projbuild;D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/liblorahub/Kconfig.projbuild"}, "build_components": ["app_trace", "app_update", "bootloader", "bootloader_support", "bsp", "bt", "bus", "cmock", "console", "cxx", "driver", "efuse", "esp-tls", "esp_adc", "esp_app_format", "esp_coex", "esp_common", "esp_eth", "esp_event", "esp_gdbstub", "esp_hid", "esp_http_client", "esp_http_server", "esp_https_ota", "esp_https_server", "esp_hw_support", "esp_lcd", "esp_local_ctrl", "esp_mm", "esp_netif", "esp_netif_stack", "esp_partition", "esp_phy", "esp_pm", "esp_psram", "esp_ringbuf", "esp_rom", "esp_system", "esp_timer", "esp_wifi", "espcoredump", "esptool_py", "fatfs", "freertos", "hal", "heap", "http_parser", "i2c_devices", "idf_test", "ieee802154", "iot_button", "json", "liblorahub", "log", "lora", "lvgl", "lwip", "main", "mbedtls", "mqtt", "newlib", "nvs_flash", "openthread", "partition_table", "perfmon", "protobuf-c", "protocomm", "pthread", "radio_drivers", "sdmmc", "smtc_ral", "soc", "spi_flash", "spiffs", "tcp_transport", "touch_element", "ulp", "unity", "usb", "vfs", "wear_levelling", "wifi_provisioning", "wpa_supplicant", "xtensa", ""], "build_component_paths": ["C:/Espressif/frameworks/esp-idf-v5.1/components/app_trace", "C:/Espressif/frameworks/esp-idf-v5.1/components/app_update", "C:/Espressif/frameworks/esp-idf-v5.1/components/bootloader", "C:/Espressif/frameworks/esp-idf-v5.1/components/bootloader_support", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/bsp", "C:/Espressif/frameworks/esp-idf-v5.1/components/bt", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/bus", "C:/Espressif/frameworks/esp-idf-v5.1/components/cmock", "C:/Espressif/frameworks/esp-idf-v5.1/components/console", "C:/Espressif/frameworks/esp-idf-v5.1/components/cxx", "C:/Espressif/frameworks/esp-idf-v5.1/components/driver", "C:/Espressif/frameworks/esp-idf-v5.1/components/efuse", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp-tls", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_adc", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_app_format", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_coex", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_common", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_eth", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_event", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_gdbstub", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_hid", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_http_client", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_http_server", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_https_ota", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_https_server", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_hw_support", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_lcd", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_local_ctrl", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_mm", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_netif", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_netif_stack", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_partition", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_phy", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_pm", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_psram", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_ringbuf", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_rom", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_system", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_timer", "C:/Espressif/frameworks/esp-idf-v5.1/components/esp_wifi", "C:/Espressif/frameworks/esp-idf-v5.1/components/espcoredump", "C:/Espressif/frameworks/esp-idf-v5.1/components/esptool_py", "C:/Espressif/frameworks/esp-idf-v5.1/components/fatfs", "C:/Espressif/frameworks/esp-idf-v5.1/components/freertos", "C:/Espressif/frameworks/esp-idf-v5.1/components/hal", "C:/Espressif/frameworks/esp-idf-v5.1/components/heap", "C:/Espressif/frameworks/esp-idf-v5.1/components/http_parser", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/i2c_devices", "C:/Espressif/frameworks/esp-idf-v5.1/components/idf_test", "C:/Espressif/frameworks/esp-idf-v5.1/components/ieee802154", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/iot_button", "C:/Espressif/frameworks/esp-idf-v5.1/components/json", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/liblorahub", "C:/Espressif/frameworks/esp-idf-v5.1/components/log", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/lora", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/lvgl", "C:/Espressif/frameworks/esp-idf-v5.1/components/lwip", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/main", "C:/Espressif/frameworks/esp-idf-v5.1/components/mbedtls", "C:/Espressif/frameworks/esp-idf-v5.1/components/mqtt", "C:/Espressif/frameworks/esp-idf-v5.1/components/newlib", "C:/Espressif/frameworks/esp-idf-v5.1/components/nvs_flash", "C:/Espressif/frameworks/esp-idf-v5.1/components/openthread", "C:/Espressif/frameworks/esp-idf-v5.1/components/partition_table", "C:/Espressif/frameworks/esp-idf-v5.1/components/perfmon", "C:/Espressif/frameworks/esp-idf-v5.1/components/protobuf-c", "C:/Espressif/frameworks/esp-idf-v5.1/components/protocomm", "C:/Espressif/frameworks/esp-idf-v5.1/components/pthread", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/radio_drivers", "C:/Espressif/frameworks/esp-idf-v5.1/components/sdmmc", "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/components/smtc_ral", "C:/Espressif/frameworks/esp-idf-v5.1/components/soc", "C:/Espressif/frameworks/esp-idf-v5.1/components/spi_flash", "C:/Espressif/frameworks/esp-idf-v5.1/components/spiffs", "C:/Espressif/frameworks/esp-idf-v5.1/components/tcp_transport", "C:/Espressif/frameworks/esp-idf-v5.1/components/touch_element", "C:/Espressif/frameworks/esp-idf-v5.1/components/ulp", "C:/Espressif/frameworks/esp-idf-v5.1/components/unity", "C:/Espressif/frameworks/esp-idf-v5.1/components/usb", "C:/Espressif/frameworks/esp-idf-v5.1/components/vfs", "C:/Espressif/frameworks/esp-idf-v5.1/components/wear_levelling", "C:/Espressif/frameworks/esp-idf-v5.1/components/wifi_provisioning", "C:/Espressif/frameworks/esp-idf-v5.1/components/wpa_supplicant", "C:/Espressif/frameworks/esp-idf-v5.1/components/xtensa", ""], "debug_prefix_map_gdbinit": ""}