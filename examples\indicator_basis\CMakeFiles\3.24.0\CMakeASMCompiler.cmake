set(CMAKE_ASM_COMPILER "C:/Espressif/tools/xtensa-esp32s3-elf/esp-12.2.0_20230208/xtensa-esp32s3-elf/bin/xtensa-esp32s3-elf-gcc.exe")
set(CMAKE_ASM_COMPILER_ARG1 "")
set(CMAKE_AR "C:/ProgramData/LLVM for Renesas RISC-V 19.1.7.202501/bin/llvm-ar.exe")
set(CMAKE_ASM_COMPILER_AR "C:/Espressif/tools/xtensa-esp32s3-elf/esp-12.2.0_20230208/xtensa-esp32s3-elf/bin/xtensa-esp32s3-elf-gcc-ar.exe")
set(CMAKE_RANLIB "C:/ProgramData/LLVM for Renesas RISC-V 19.1.7.202501/bin/llvm-ranlib.exe")
set(CMAKE_ASM_COMPILER_RANLIB "C:/Espressif/tools/xtensa-esp32s3-elf/esp-12.2.0_20230208/xtensa-esp32s3-elf/bin/xtensa-esp32s3-elf-gcc-ranlib.exe")
set(CMAKE_LINKER "C:/ProgramData/LLVM for Renesas RISC-V 19.1.7.202501/bin/ld.lld.exe")
set(CMAKE_MT "")
set(CMAKE_ASM_COMPILER_LOADED 1)
set(CMAKE_ASM_COMPILER_ID "GNU")
set(CMAKE_ASM_COMPILER_VERSION "")
set(CMAKE_ASM_COMPILER_ENV_VAR "ASM")




set(CMAKE_ASM_IGNORE_EXTENSIONS h;H;o;O;obj;OBJ;def;DEF;rc;RC)
set(CMAKE_ASM_LINKER_PREFERENCE 0)


