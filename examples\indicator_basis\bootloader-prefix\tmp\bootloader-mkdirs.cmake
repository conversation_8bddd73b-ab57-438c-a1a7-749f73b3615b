# Distributed under the OSI-approved BSD 3-Clause License.  See accompanying
# file Copyright.txt or https://cmake.org/licensing for details.

cmake_minimum_required(VERSION 3.5)

file(MAKE_DIRECTORY
  "C:/Espressif/frameworks/esp-idf-v5.1/components/bootloader/subproject"
  "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader"
  "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix"
  "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/tmp"
  "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp"
  "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src"
  "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp"
)

set(configSubDirs )
foreach(subDir IN LISTS configSubDirs)
    file(MAKE_DIRECTORY "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/${subDir}")
endforeach()
if(cfgdir)
  file(MAKE_DIRECTORY "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp${cfgdir}") # cfgdir has leading slash
endif()
