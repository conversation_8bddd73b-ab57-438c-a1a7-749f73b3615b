# Target labels
 bootloader
# Source files and their labels
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/CMakeFiles/bootloader
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/CMakeFiles/bootloader.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/CMakeFiles/bootloader-complete.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
