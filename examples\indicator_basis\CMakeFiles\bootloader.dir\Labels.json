{"sources": [{"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/CMakeFiles/bootloader"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/CMakeFiles/bootloader.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/CMakeFiles/bootloader-complete.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "D:/Programs/ESP-IDF/SenseCAP_Indicator_ESP32/examples/indicator_basis/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}